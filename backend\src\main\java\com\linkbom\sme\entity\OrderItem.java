package com.linkbom.sme.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单明细实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "order_items")
public class OrderItem extends BaseEntity {
    
    @NotNull(message = "订单不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private Order order;
    
    @NotBlank(message = "产品名称不能为空")
    @Column(name = "product_name", nullable = false)
    private String productName;
    
    @Column(name = "specification")
    private String specification;
    
    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    @Column(name = "quantity", nullable = false)
    private Integer quantity;
    
    @Column(name = "unit")
    private String unit = "个";
    
    @NotNull(message = "单价不能为空")
    @Positive(message = "单价必须大于0")
    @Column(name = "unit_price", precision = 15, scale = 2, nullable = false)
    private BigDecimal unitPrice;
    
    @Column(name = "total_price", precision = 15, scale = 2, nullable = false)
    private BigDecimal totalPrice = BigDecimal.ZERO;
    
    @Column(name = "actual_unit_cost", precision = 15, scale = 2)
    private BigDecimal actualUnitCost = BigDecimal.ZERO;
    
    @Column(name = "actual_total_cost", precision = 15, scale = 2)
    private BigDecimal actualTotalCost = BigDecimal.ZERO;
    
    @Column(name = "notes")
    private String notes;
    
    /**
     * 计算总价和总成本
     */
    @PrePersist
    @PreUpdate
    public void calculateTotals() {
        if (quantity != null && unitPrice != null) {
            this.totalPrice = unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
        if (quantity != null && actualUnitCost != null) {
            this.actualTotalCost = actualUnitCost.multiply(BigDecimal.valueOf(quantity));
        }
    }
}
