package com.linkbom.sme.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 客户实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "customers")
public class Customer extends BaseEntity {
    
    @NotBlank(message = "客户名称不能为空")
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "contact_person")
    private String contactPerson;
    
    @Column(name = "phone")
    private String phone;
    
    @Email(message = "邮箱格式不正确")
    @Column(name = "email")
    private String email;
    
    @Column(name = "address")
    private String address;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "level", nullable = false)
    private CustomerLevel level = CustomerLevel.B;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "credit_rating", nullable = false)
    private CreditRating creditRating = CreditRating.GOOD;
    
    @Column(name = "payment_term")
    private Integer paymentTerm = 30; // 付款期限（天）
    
    @Column(name = "total_orders")
    private Integer totalOrders = 0;
    
    @Column(name = "total_amount", precision = 15, scale = 2)
    private BigDecimal totalAmount = BigDecimal.ZERO;
    
    @Column(name = "paid_amount", precision = 15, scale = 2)
    private BigDecimal paidAmount = BigDecimal.ZERO;
    
    @Column(name = "unpaid_amount", precision = 15, scale = 2)
    private BigDecimal unpaidAmount = BigDecimal.ZERO;
    
    @Column(name = "last_order_date")
    private LocalDate lastOrderDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CustomerStatus status = CustomerStatus.ACTIVE;
    
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;
    
    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Quotation> quotations;
    
    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Order> orders;
    
    /**
     * 客户等级枚举
     */
    public enum CustomerLevel {
        A("重要客户"),
        B("普通客户");
        
        private final String description;
        
        CustomerLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 信用等级枚举
     */
    public enum CreditRating {
        EXCELLENT("优秀"),
        GOOD("良好"),
        FAIR("一般"),
        POOR("较差");
        
        private final String description;
        
        CreditRating(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 客户状态枚举
     */
    public enum CustomerStatus {
        ACTIVE("活跃"),
        INACTIVE("非活跃"),
        BLACKLIST("黑名单");
        
        private final String description;
        
        CustomerStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
