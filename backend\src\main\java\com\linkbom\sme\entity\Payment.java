package com.linkbom.sme.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收款记录实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "payments")
public class Payment extends BaseEntity {
    
    @NotNull(message = "应收款不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "receivable_id", nullable = false)
    private Receivable receivable;
    
    @NotNull(message = "收款金额不能为空")
    @Positive(message = "收款金额必须大于0")
    @Column(name = "amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal amount;
    
    @Column(name = "payment_date", nullable = false)
    private LocalDate paymentDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method", nullable = false)
    private PaymentMethod paymentMethod;
    
    @Column(name = "reference_no")
    private String referenceNo;
    
    @Column(name = "notes")
    private String notes;
    
    /**
     * 收款方式枚举
     */
    public enum PaymentMethod {
        CASH("现金"),
        BANK_TRANSFER("银行转账"),
        ALIPAY("支付宝"),
        WECHAT("微信支付"),
        CHECK("支票"),
        OTHER("其他");
        
        private final String description;
        
        PaymentMethod(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
