package com.linkbom.sme.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 应收款实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "receivables")
public class Receivable extends BaseEntity {
    
    @NotNull(message = "订单不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private Order order;
    
    @NotNull(message = "客户不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;
    
    @NotNull(message = "应收金额不能为空")
    @Positive(message = "应收金额必须大于0")
    @Column(name = "amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal amount;
    
    @Column(name = "paid_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal paidAmount = BigDecimal.ZERO;
    
    @Column(name = "remaining_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal remainingAmount = BigDecimal.ZERO;
    
    @Column(name = "due_date", nullable = false)
    private LocalDate dueDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ReceivableStatus status = ReceivableStatus.PENDING;
    
    @Column(name = "aging_days")
    private Integer agingDays = 0;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "aging_category", nullable = false)
    private AgingCategory agingCategory = AgingCategory.CURRENT;
    
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;
    
    @OneToMany(mappedBy = "receivable", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Payment> payments;
    
    /**
     * 应收款状态枚举
     */
    public enum ReceivableStatus {
        PENDING("待收款"),
        PARTIAL("部分收款"),
        PAID("已收款"),
        OVERDUE("逾期"),
        WRITTEN_OFF("核销");
        
        private final String description;
        
        ReceivableStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 账龄分类枚举
     */
    public enum AgingCategory {
        CURRENT("30天内"),
        DAYS_30_60("30-60天"),
        DAYS_60_90("60-90天"),
        OVER_90("90天以上");
        
        private final String description;
        
        AgingCategory(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 计算剩余金额
     */
    @PrePersist
    @PreUpdate
    public void calculateRemainingAmount() {
        if (amount != null && paidAmount != null) {
            this.remainingAmount = amount.subtract(paidAmount);
        }
    }
}
