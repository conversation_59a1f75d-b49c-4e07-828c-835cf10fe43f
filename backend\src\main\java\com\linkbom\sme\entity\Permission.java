package com.linkbom.sme.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * 权限实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "permissions")
public class Permission extends BaseEntity {
    
    @NotBlank(message = "权限代码不能为空")
    @Column(name = "code", unique = true, nullable = false)
    private String code;
    
    @NotBlank(message = "权限名称不能为空")
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "resource")
    private String resource;
    
    @Column(name = "action")
    private String action;
    
    @ManyToMany(mappedBy = "permissions")
    private Set<Role> roles;
}
