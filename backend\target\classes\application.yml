# 小微企业经营管理系统配置文件
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: sme-management-backend
  
  # 数据库配置
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME:sme_user}
    password: ${DB_PASSWORD:sme_password}
    driver-class-name: org.postgresql.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null

# JWT配置
jwt:
  secret: ${JWT_SECRET:linkbom-sme-management-system-jwt-secret-key-2024}
  expiration: 86400000 # 24小时
  refresh-expiration: 604800000 # 7天

# 日志配置
logging:
  level:
    com.linkbom.sme: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 跨域配置
cors:
  allowed-origins: 
    - http://localhost:3000
    - http://127.0.0.1:3000
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"
  allow-credentials: true

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop
  h2:
    console:
      enabled: true

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
logging:
  level:
    com.linkbom.sme: INFO
    org.springframework.security: WARN
