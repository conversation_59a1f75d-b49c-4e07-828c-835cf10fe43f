package com.linkbom.sme.repository;

import com.linkbom.sme.entity.Customer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客户Repository
 */
@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long> {
    
    /**
     * 根据客户名称模糊查询
     */
    Page<Customer> findByNameContainingIgnoreCaseAndDeletedFalse(String name, Pageable pageable);
    
    /**
     * 根据客户等级查询
     */
    Page<Customer> findByLevelAndDeletedFalse(Customer.CustomerLevel level, Pageable pageable);
    
    /**
     * 根据客户状态查询
     */
    Page<Customer> findByStatusAndDeletedFalse(Customer.CustomerStatus status, Pageable pageable);
    
    /**
     * 查询所有未删除的客户
     */
    Page<Customer> findByDeletedFalse(Pageable pageable);
    
    /**
     * 根据多个条件查询客户
     */
    @Query("SELECT c FROM Customer c WHERE " +
           "(:name IS NULL OR LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:level IS NULL OR c.level = :level) AND " +
           "(:status IS NULL OR c.status = :status) AND " +
           "c.deleted = false")
    Page<Customer> findByConditions(@Param("name") String name,
                                   @Param("level") Customer.CustomerLevel level,
                                   @Param("status") Customer.CustomerStatus status,
                                   Pageable pageable);
    
    /**
     * 获取客户贡献排行榜
     */
    @Query("SELECT c FROM Customer c WHERE c.deleted = false ORDER BY c.totalAmount DESC")
    List<Customer> findTopCustomersByRevenue(Pageable pageable);
}
