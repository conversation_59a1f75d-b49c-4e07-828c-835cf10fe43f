package com.linkbom.sme.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 报价单实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "quotations")
public class Quotation extends BaseEntity {
    
    @NotBlank(message = "报价单号不能为空")
    @Column(name = "quotation_no", unique = true, nullable = false)
    private String quotationNo;
    
    @NotNull(message = "客户不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;
    
    @NotBlank(message = "报价标题不能为空")
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "total_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal totalAmount = BigDecimal.ZERO;
    
    @Column(name = "estimated_cost", precision = 15, scale = 2, nullable = false)
    private BigDecimal estimatedCost = BigDecimal.ZERO;
    
    @Column(name = "estimated_profit", precision = 15, scale = 2, nullable = false)
    private BigDecimal estimatedProfit = BigDecimal.ZERO;
    
    @Column(name = "profit_margin", precision = 5, scale = 2, nullable = false)
    private BigDecimal profitMargin = BigDecimal.ZERO;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private QuotationStatus status = QuotationStatus.DRAFT;
    
    @Column(name = "valid_until")
    private LocalDate validUntil;
    
    @Column(name = "salesperson")
    private String salesperson;
    
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;
    
    @OneToMany(mappedBy = "quotation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<QuotationItem> items;
    
    @OneToOne(mappedBy = "quotation", fetch = FetchType.LAZY)
    private Order convertedOrder;
    
    /**
     * 报价单状态枚举
     */
    public enum QuotationStatus {
        DRAFT("草稿"),
        PENDING("待审核"),
        APPROVED("已审核"),
        SENT("已发送"),
        ACCEPTED("已接受"),
        REJECTED("已拒绝"),
        EXPIRED("已过期"),
        CONVERTED("已转订单");
        
        private final String description;
        
        QuotationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
